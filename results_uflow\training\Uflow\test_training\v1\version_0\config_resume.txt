==== CONFIGURATION TRAINING ====

** DataModule Parameters **
name: test_training
root: C:\Users\<USER>\Documents\uflow\Datasets\test_training
normal_dir: train/good
abnormal_dir: test/defective
normal_test_dir: test/good
mask_dir: masks/defective
train_batch_size: 2
eval_batch_size: 1
num_workers: 0
normal_split_ratio: 0.2
test_split_ratio: 0.2
val_split_ratio: 0.5

** Model Parameters **
backbone: resnet18
flow_steps: 4
affine_clamp: 2.0
affine_subnet_channels_ratio: 1.0
permute_soft: False

** Engine Parameters **
max_epochs: 1
accumulate_grad_batches: 1
check_val_every_n_epoch: 1
