import warnings
import os
import torch
from pathlib import Path
from anomalib.data import Folder
from anomalib.models import Uflow
from anomalib.engine import Engine
from lightning.pytorch.loggers import TensorBoardLogger
from lightning.pytorch import seed_everything
from utils.sauvegarde import save_config_resume
from utils.sauvegarde import save_script_copy
from utils.sauvegarde import save_config_totale
from utils.sauvegarde import save_visualiser_tensorboard
from anomalib.callbacks.checkpoint import ModelCheckpoint
from utils.save_outputs import SaveOutputsCallback


# Completely suppress all warnings before training
warnings.filterwarnings("ignore")

# Clear CUDA cache and set memory allocation config
torch.cuda.empty_cache()
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"


# Optimisation CUDA
torch.set_float32_matmul_precision('medium')

# ========== CONFIGURATION DES CHEMINS ==========
MODEL_NAME = "Uflow"

# Dossier contenant le dataset à utiliser pour training
DATASET_ROOT = Path("datasets/test_training").resolve()


# ex: "TPI" si DATASET_ROOT = .../datasets/TPI
DATASET_NAME = DATASET_ROOT.name  

# utilisé juste pour chemin du ckpt
LOGGER_NAME = f"{MODEL_NAME}/{DATASET_NAME}"

# Dossier principal où tout sera enregistré (logs, modèles, backups, etc.)
ROOT_BASE = Path("results_uflow/training").resolve()

# Sous-dossiers du dataset
NORMAL_DIR = "train/good"
ABNORMAL_DIR = "test/defective"
NORMAL_TEST_DIR = "test/good"
MASK_DIR = "masks/defective"

# c'est la racine Anomalib maintenant
train_results_path = ROOT_BASE

# ========== FIN CONFIGURATION ==========

def main():
    seed_everything(42)

    # Logger personalisé
    os.environ["TF_ENABLE_ONEDNN_OPTS"] = "0"
    logger = TensorBoardLogger(
        save_dir=train_results_path,
        name=f"{MODEL_NAME}/{DATASET_NAME}/latest"
    )

    # Modèle
    model = Uflow(
        backbone="resnet18", #'mcait', 'resnet18', 'wide_resnet50_2'.
        flow_steps=4,
        affine_clamp=2.0,
        affine_subnet_channels_ratio=1.0,
        permute_soft=False,
        pre_processor=True,
        post_processor=True,
        evaluator=True,
        visualizer=True
    )

    # DataModule
    datamodule = Folder(
        name=DATASET_NAME,
        root=DATASET_ROOT,
        normal_dir=NORMAL_DIR,
        abnormal_dir=ABNORMAL_DIR,
        normal_test_dir=NORMAL_TEST_DIR,
        mask_dir=MASK_DIR,
        normal_split_ratio=0.2,
        test_split_mode="from_dir",
        test_split_ratio=0.2,
        val_split_mode="same_as_test",
        val_split_ratio=0.5,
        train_batch_size=2,
        eval_batch_size=1,
        num_workers=0
    )

    # callback
    checkpoint_callback = ModelCheckpoint(
        dirpath=None,
        filename="epoch_{epoch:03d}-loss={loss:.4f}",
        monitor="loss",
        save_top_k=5
    )

    # Engine
    engine = Engine(
        max_epochs=1,
        accumulate_grad_batches=1,
        check_val_every_n_epoch=1,
        devices="auto",
        accelerator="auto",
        strategy="auto",
        num_nodes=1,
        inference_mode=True,
        use_distributed_sampler=False,
        default_root_dir=train_results_path,
        logger=logger,
        callbacks=[checkpoint_callback, SaveOutputsCallback()]   
    )

    # === Lancer l'entraînement ===
    engine.fit(datamodule=datamodule, model=model)
    print("✅ Meilleur checkpoint :", checkpoint_callback.best_model_path)
    print("🎯 Score (val_loss)    :", checkpoint_callback.best_model_score)

    # === Test (si le checkpoint existe) ===
    ckpt_path = Path(checkpoint_callback.best_model_path)
    if ckpt_path.exists():
        engine.test(datamodule=datamodule, model=model, ckpt_path=ckpt_path)
    else:
        print(f"⚠️ Checkpoint non trouvé : {ckpt_path}")

    # === Sauvegardes personnalisées ===
    real_run_dir = Path(logger.log_dir)
    save_script_copy(real_run_dir, source_script=Path(__file__))
    save_config_resume(datamodule, model, engine, output_path=real_run_dir / "config_resume.txt")
    save_config_totale(datamodule, model, engine, output_path=real_run_dir / "config_totale.txt")
    save_visualiser_tensorboard(real_run_dir)

    print("\n✅ Tous les fichiers ont été sauvegardés dans :", real_run_dir)


if __name__ == '__main__':
    main()