# sauvegarde.py

import os
import shutil
from pathlib import Path
from datetime import datetime
import json
import csv
import matplotlib.pyplot as plt
from tensorboard.backend.event_processing import event_accumulator
import numpy as np
import cv2
from PIL import Image



def save_script_copy(output_dir: Path, source_script: Path):
    """Copie le script source (ex: training.py) dans un dossier."""
    try:
        shutil.copy(source_script, output_dir / "script_backup.py")
        print(f" Copie du script enregistrée dans : {output_dir}")
    except Exception as e:
        print(f" Erreur lors de la sauvegarde du script : {e}")


def save_config_resume(datamodule, model, engine, output_path):
    """Sauvegarde uniquement les paramètres définis explicitement dans le script."""    
    with open(output_path, "w") as f:
        f.write("==== CONFIGURATION TRAINING ====\n\n")

        # Enregistrer uniquement les paramètres utiles du DataModule
        f.write("** DataModule Parameters **\n")
        f.write(f"name: {datamodule.name}\n")
        f.write(f"root: {datamodule.root}\n")
        f.write(f"normal_dir: {datamodule.normal_dir}\n")
        f.write(f"abnormal_dir: {datamodule.abnormal_dir}\n")
        f.write(f"normal_test_dir: {datamodule.normal_test_dir}\n")
        f.write(f"mask_dir: {datamodule.mask_dir}\n")
        f.write(f"train_batch_size: {datamodule.train_batch_size}\n")
        f.write(f"eval_batch_size: {datamodule.eval_batch_size}\n")
        f.write(f"num_workers: {datamodule.num_workers}\n")
        f.write(f"normal_split_ratio: {datamodule.normal_split_ratio}\n")
        f.write(f"test_split_ratio: {datamodule.test_split_ratio}\n")
        f.write(f"val_split_ratio: {datamodule.val_split_ratio}\n")

        # Enregistrer uniquement les paramètres utiles du modèle
        f.write("\n** Model Parameters **\n")
        f.write(f"backbone: {model.backbone}\n")
        f.write(f"flow_steps: {model.flow_steps}\n")
        f.write(f"affine_clamp: {model.affine_clamp}\n")
        f.write(f"affine_subnet_channels_ratio: {model.affine_subnet_channels_ratio}\n")
        f.write(f"permute_soft: {model.permute_soft}\n")

        # Vérifier que l'Engine a bien un `trainer`
        if hasattr(engine, "trainer") and engine.trainer is not None:
            trainer = engine.trainer
            f.write("\n** Engine Parameters **\n")
            f.write(f"max_epochs: {trainer.max_epochs}\n")
            f.write(f"accumulate_grad_batches: {trainer.accumulate_grad_batches}\n")
            f.write(f"check_val_every_n_epoch: {trainer.check_val_every_n_epoch}\n")
            

def save_config_totale(datamodule, model, engine, output_path):
    """Sauvegarde détaillée des paramètres du datamodule, modèle, engine et trainer dans un fichier texte."""
    def dump_all_attributes(obj, f, title=None):
        if title:
            f.write(f"\n-------------------- {title} --------------------\n")
        for attr in sorted(dir(obj)):
            if attr.startswith("_"):
                continue
            try:
                value = getattr(obj, attr)
                f.write(f"{attr}: {value}\n")
            except Exception as e:
                f.write(f"{attr}: Erreur ({e})\n")

    with open(output_path, "w", encoding="utf-8") as f:
        f.write("==== CONFIGURATION TRAINING (VERBOSE) ====\n")

        # === DataModule ===
        f.write("\n--------------------------------------------------------\n")
        f.write("** DataModule Parameters **\n")
        f.write("--------------------------------------------------------\n")
        for key, value in vars(datamodule).items():
            f.write(f"{key}: {value}\n")

        # === Model ===
        f.write("\n--------------------------------------------------------\n")
        f.write("** Model Parameters (via vars) **\n")
        f.write("--------------------------------------------------------\n")
        try:
            for key, value in vars(model).items():
                f.write(f"{key}: {value}\n")
        except TypeError:
            f.write("model has no __dict__ (vars).\n")

        f.write("\n--------------------------------------------------------\n")
        f.write("** Model Structure (named_children) **\n")
        f.write("--------------------------------------------------------\n")
        for name, layer in model.named_children():
            f.write(f"{name}:\n{layer}\n")

        f.write("\n--------------------------------------------------------\n")
        f.write("** Model Parameters (named_parameters) **\n")
        f.write("--------------------------------------------------------\n")
        for name, param in model.named_parameters():
            f.write(f"{name} - shape: {tuple(param.shape)}\n")

        # === Engine ===
        f.write("\n--------------------------------------------------------\n")
        f.write("** Engine Parameters **\n")
        f.write("--------------------------------------------------------\n")
        for key, value in vars(engine).items():
            f.write(f"{key}: {value}\n")

        # === Trainer ===
        f.write("\n--------------------------------------------------------\n")
        f.write("** Trainer Parameters (réel, après .fit) **\n")
        f.write("--------------------------------------------------------\n")
        try:
            trainer = engine.trainer

            for key, value in vars(trainer).items():
                f.write(f"{key}: {value}\n")

            dump_all_attributes(trainer, f, title="Trainer Attributes - ALL")

            f.write("\n--------------------------------------------------------\n")
            f.write("** Trainer Device & Strategy Info **\n")
            f.write("--------------------------------------------------------\n")
            f.write(f"device_ids: {getattr(trainer, 'device_ids', 'N/A')}\n")
            f.write(f"root_device: {getattr(trainer.strategy, 'root_device', 'N/A')}\n")
            f.write(f"strategy: {trainer.strategy.__class__.__name__}\n")
            f.write(f"accelerator: {trainer.accelerator.__class__.__name__}\n")

            f.write("\n--------------------------------------------------------\n")
            f.write("** Callbacks **\n")
            f.write("--------------------------------------------------------\n")
            for cb in trainer.callbacks:
                f.write(f"{type(cb).__name__}: {cb}\n")

            checkpoint_cb = next((cb for cb in trainer.callbacks if "checkpoint" in type(cb).__name__.lower()), None)
            if checkpoint_cb:
                f.write("\n--------------------------------------------------------\n")
                f.write("** Best Checkpoint **\n")
                f.write("--------------------------------------------------------\n")
                f.write(f"Path: {checkpoint_cb.best_model_path or '(non défini)'}\n")
                f.write(f"Score: {checkpoint_cb.best_model_score or '(non défini)'}\n")

        except Exception as e:
            f.write(f"Erreur lors de l'accès au trainer : {e}\n")


def save_visualiser_tensorboard(log_dir):
    """Analyse les fichiers TensorBoard et exporte les scalars, courbes et résumé."""
    from tensorboard.backend.event_processing import event_accumulator
    import matplotlib.pyplot as plt
    import json
    import csv
    from datetime import datetime

    print("📊 Lecture des logs TensorBoard...")
    ea = event_accumulator.EventAccumulator(str(log_dir))
    ea.Reload()

    scalars_disponibles = ea.Tags().get("scalars", [])
    all_scalars = {}

    for tag in scalars_disponibles:
        events = ea.Scalars(tag)
        data = [{"step": e.step, "wall_time": e.wall_time, "value": e.value} for e in events]
        all_scalars[tag] = data

    # Export JSON
    json_path = os.path.join(log_dir, "loss_scalars_export.json")
    with open(json_path, "w") as f_json:
        json.dump(all_scalars, f_json, indent=2)

    # Export CSV
    csv_path = os.path.join(log_dir, "loss_scalars_export.csv")
    with open(csv_path, "w", newline="") as f_csv:
        writer = csv.writer(f_csv)
        writer.writerow(["scalar_name", "step", "wall_time", "value"])
        for tag, data in all_scalars.items():
            for entry in data:
                writer.writerow([tag, entry["step"], entry["wall_time"], entry["value"]])

    # Génération courbe loss
    def plot_loss_curve(steps, values, title, filename=None, show=False):
        plt.figure(figsize=(10, 5))
        plt.plot(steps, values, label="loss", color="black")
        plt.xlabel("Step")
        plt.ylabel("Loss")
        plt.title(title)
        plt.grid(True)
        plt.legend()
        plt.tight_layout()
        if filename:
            plt.savefig(filename, dpi=300)
        if show:
            plt.show()
        else:
            plt.close()

    if "loss" in all_scalars:
        loss_data = all_scalars["loss"]
        steps = [d["step"] for d in loss_data]
        values = [d["value"] for d in loss_data]
        png_path = os.path.join(log_dir, "loss_curve.png")
        plot_loss_curve(steps, values, title="Courbe de perte (loss)", filename=png_path)

    # Génération du résumé TXT
    info_lines = []
    info_lines.append("📊 Résumé complet du run TensorBoard")
    info_lines.append("=" * 50)

    # Temps
    all_times = [d["wall_time"] for data in all_scalars.values() for d in data]
    if all_times:
        start_time = min(all_times)
        end_time = max(all_times)
        duration_sec = end_time - start_time
        start_str = datetime.fromtimestamp(start_time).strftime("%Y-%m-%d %H:%M:%S")
        end_str = datetime.fromtimestamp(end_time).strftime("%Y-%m-%d %H:%M:%S")

        info_lines.append(f"🕐 Début : {start_str}")
        info_lines.append(f"🕛 Fin   : {end_str}")
        info_lines.append(f"⏱️ Durée totale : {duration_sec:.2f} sec ({duration_sec / 60:.2f} min)")

    # Infos scalars
    info_lines.append("\n📈 Scalars disponibles :")
    for tag, data in all_scalars.items():
        values = [d["value"] for d in data]
        info_lines.append(f"    • {tag} : {len(values)} points | min = {min(values):.6f}, max = {max(values):.6f}")

    # Min loss
    if "loss" in all_scalars:
        loss_data = all_scalars["loss"]
        values = [d["value"] for d in loss_data]
        steps = [d["step"] for d in loss_data]
        min_loss = min(values)
        idx = values.index(min_loss)
        min_step = steps[idx]
        min_time = loss_data[idx]["wall_time"]
        min_time_str = datetime.fromtimestamp(min_time).strftime("%Y-%m-%d %H:%M:%S")

        info_lines.append(f"\n🔽 Min Loss : {min_loss:.6f}")
        info_lines.append(f"    • Step : {min_step}")
        info_lines.append(f"    • Temps : {min_time_str}")

    # Epochs
    if "epoch" in all_scalars:
        last_epoch = max([entry["value"] for entry in all_scalars["epoch"]])
        info_lines.append(f"\n🗓️ Nombre total d'epochs : {int(last_epoch)}")

    summary_path = os.path.join(log_dir, "run_summary.txt")
    with open(summary_path, "w", encoding="utf-8") as f:
        f.write("\n".join(info_lines))
    print("✅ Scalars disponibles :", ea.Tags().get("scalars", []))

    print("✅ Visualisation TensorBoard exportée.")


def save_prediction_outputs(predictions, dataset_root, predict_root):
    """Sauvegarde les résultats de prédiction dans une structure de dossiers versionnée."""
    # === Dossiers de sortie ===
    save_dir_gray = predict_root / "gray"
    save_dir_png = predict_root / "png"
    save_dir_images = save_dir_png / "images"
    save_dir_maps = save_dir_png / "anomaly_maps"
    save_dir_masks = save_dir_png / "pred_masks"
    for path in [save_dir_gray, save_dir_images, save_dir_maps, save_dir_masks]:
        os.makedirs(path, exist_ok=True)

    # ========== SAUVEGARDE ==========
    images_list, anomaly_maps_list, pred_masks_list = [], [], []
    for pred in predictions:
        image = pred.image.squeeze().detach().cpu().numpy()
        anomaly_map = pred.anomaly_map.squeeze().detach().cpu().numpy()
        pred_mask = pred.pred_mask.squeeze().detach().cpu().numpy()

        if image.shape[0] == 1:
            image = image.squeeze(0)
        elif image.shape[0] == 3:
            image = np.transpose(image, (1, 2, 0))
            image = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            raise ValueError(f"Format inattendu pour l'image : {image.shape}")

        images_list.append(image)
        anomaly_maps_list.append(anomaly_map)
        pred_masks_list.append(pred_mask)

    # === Sauvegarde .npy ===
    np.save(save_dir_gray / "images_gray.npy", np.stack(images_list))
    np.save(save_dir_gray / "anomaly_maps_gray.npy", np.stack(anomaly_maps_list))
    np.save(save_dir_gray / "pred_masks_gray.npy", (np.stack(pred_masks_list) > 0.5).astype(bool))

    # === Sauvegarde PNG ===
    for idx, (image, anomaly_map, pred_mask) in enumerate(zip(images_list, anomaly_maps_list, pred_masks_list)):
        image_norm = (image - image.min()) / (image.max() - image.min() + 1e-8)
        anomaly_map_norm = (anomaly_map - anomaly_map.min()) / (anomaly_map.max() - anomaly_map.min() + 1e-8)
        pred_mask_bin = (pred_mask > 0.5).astype(np.uint8) * 255

        Image.fromarray((image_norm * 255).astype(np.uint8), mode='L').save(save_dir_images / f"image_{idx:04d}.png")
        Image.fromarray((anomaly_map_norm * 255).astype(np.uint8), mode='L').save(save_dir_maps / f"anomaly_map_{idx:04d}.png")
        Image.fromarray(pred_mask_bin, mode='L').save(save_dir_masks / f"pred_mask_{idx:04d}.png")

    # === Résumé texte ===
    summary_path = predict_root / "prediction_summary.txt"
    with open(summary_path, "w", encoding="utf-8") as f:
        f.write("==== Résumé prédiction ====\n")
        f.write(f"📁 Dataset à tester: {dataset_root}\n")
        f.write(f"🖼️ Nombre d'images : {len(images_list)}\n")
        f.write(f"⏰ Date : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
