==== CONFIGURATION TRAINING (VERBOSE) ====

--------------------------------------------------------
** DataModule Parameters **
--------------------------------------------------------
_name: test_training
root: C:\Users\<USER>\Documents\uflow\Datasets\test_training
normal_dir: train/good
abnormal_dir: test/defective
normal_test_dir: test/good
mask_dir: masks/defective
extensions: None
_log_hyperparams: False
prepare_data_per_node: True
allow_zero_length_dataloader_with_multiple_devices: False
trainer: <lightning.pytorch.trainer.trainer.Trainer object at 0x000002132C56A710>
train_batch_size: 2
eval_batch_size: 1
num_workers: 0
test_split_mode: from_dir
test_split_ratio: 0.2
val_split_mode: same_as_test
val_split_ratio: 0.5
seed: None
train_augmentations: None
val_augmentations: None
test_augmentations: None
_samples: None
_category: 
_is_setup: True
external_collate_fn: None
normal_split_ratio: 0.2
train_data: <anomalib.data.datasets.image.folder.FolderDataset object at 0x000002132C64E2F0>
test_data: <anomalib.data.datasets.image.folder.FolderDataset object at 0x000002132C64FA90>
val_data: <anomalib.data.datasets.image.folder.FolderDataset object at 0x000002132C64FE80>
_hparams: 

--------------------------------------------------------
** Model Parameters (via vars) **
--------------------------------------------------------
training: True
_parameters: {}
_buffers: {}
_non_persistent_buffers_set: set()
_backward_pre_hooks: OrderedDict()
_backward_hooks: OrderedDict()
_is_full_backward_hook: None
_forward_hooks: OrderedDict()
_forward_hooks_with_kwargs: OrderedDict()
_forward_hooks_always_called: OrderedDict()
_forward_pre_hooks: OrderedDict()
_forward_pre_hooks_with_kwargs: OrderedDict()
_state_dict_hooks: OrderedDict()
_state_dict_pre_hooks: OrderedDict()
_load_state_dict_pre_hooks: OrderedDict()
_load_state_dict_post_hooks: OrderedDict()
_modules: {'pre_processor': PreProcessor(
  (transform): Compose(
        Resize(size=[448, 448], interpolation=InterpolationMode.BILINEAR, antialias=True)
        Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], inplace=False)
  )
  (export_transform): Compose(
        Resize(size=[448, 448], interpolation=InterpolationMode.BILINEAR, antialias=False)
        Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], inplace=False)
  )
), 'post_processor': PostProcessor(
  (_image_threshold_metric): F1AdaptiveThreshold()
  (_pixel_threshold_metric): F1AdaptiveThreshold()
  (_image_min_max_metric): MinMax()
  (_pixel_min_max_metric): MinMax()
), 'evaluator': Evaluator(
  (val_metrics): ModuleList()
  (test_metrics): ModuleList(
    (0): AUROC()
    (1): F1Score()
    (2): AUROC()
    (3): F1Score()
  )
), 'model': UflowModel(
  (feature_extractor): LayerNormFeatureExtractor(
    (feature_extractor): FeatureListNet(
      (conv1): Conv2d(3, 64, kernel_size=(7, 7), stride=(2, 2), padding=(3, 3), bias=False)
      (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (act1): ReLU(inplace=True)
      (maxpool): MaxPool2d(kernel_size=3, stride=2, padding=1, dilation=1, ceil_mode=False)
      (layer1): Sequential(
        (0): BasicBlock(
          (conv1): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (drop_block): Identity()
          (act1): ReLU(inplace=True)
          (aa): Identity()
          (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (act2): ReLU(inplace=True)
        )
        (1): BasicBlock(
          (conv1): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (drop_block): Identity()
          (act1): ReLU(inplace=True)
          (aa): Identity()
          (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (act2): ReLU(inplace=True)
        )
      )
      (layer2): Sequential(
        (0): BasicBlock(
          (conv1): Conv2d(64, 128, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
          (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (drop_block): Identity()
          (act1): ReLU(inplace=True)
          (aa): Identity()
          (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (act2): ReLU(inplace=True)
          (downsample): Sequential(
            (0): Conv2d(64, 128, kernel_size=(1, 1), stride=(2, 2), bias=False)
            (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          )
        )
        (1): BasicBlock(
          (conv1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (drop_block): Identity()
          (act1): ReLU(inplace=True)
          (aa): Identity()
          (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (act2): ReLU(inplace=True)
        )
      )
      (layer3): Sequential(
        (0): BasicBlock(
          (conv1): Conv2d(128, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
          (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (drop_block): Identity()
          (act1): ReLU(inplace=True)
          (aa): Identity()
          (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (act2): ReLU(inplace=True)
          (downsample): Sequential(
            (0): Conv2d(128, 256, kernel_size=(1, 1), stride=(2, 2), bias=False)
            (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          )
        )
        (1): BasicBlock(
          (conv1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (drop_block): Identity()
          (act1): ReLU(inplace=True)
          (aa): Identity()
          (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (act2): ReLU(inplace=True)
        )
      )
    )
    (feature_normalizations): ModuleList(
      (0): LayerNorm((64, 112, 112), eps=1e-05, elementwise_affine=True)
      (1): LayerNorm((128, 56, 56), eps=1e-05, elementwise_affine=True)
      (2): LayerNorm((256, 28, 28), eps=1e-05, elementwise_affine=True)
    )
  )
  (flow): GraphINN(
    (module_list): ModuleList(
      (0): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(128, 256, kernel_size=(3, 3), stride=(1, 1), padding=same)
        )
      )
      (1): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(128, 128, kernel_size=(1, 1), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1), padding=same)
        )
      )
      (2): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(128, 256, kernel_size=(3, 3), stride=(1, 1), padding=same)
        )
      )
      (3): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(128, 128, kernel_size=(1, 1), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1), padding=same)
        )
      )
      (4): Split()
      (5): IRevNetUpsampling()
      (6): Concat()
      (7): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(80, 80, kernel_size=(3, 3), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(80, 160, kernel_size=(3, 3), stride=(1, 1), padding=same)
        )
      )
      (8): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(80, 80, kernel_size=(1, 1), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(80, 160, kernel_size=(1, 1), stride=(1, 1), padding=same)
        )
      )
      (9): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(80, 80, kernel_size=(3, 3), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(80, 160, kernel_size=(3, 3), stride=(1, 1), padding=same)
        )
      )
      (10): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(80, 80, kernel_size=(1, 1), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(80, 160, kernel_size=(1, 1), stride=(1, 1), padding=same)
        )
      )
      (11): Split()
      (12): IRevNetUpsampling()
      (13): Concat()
      (14): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(42, 42, kernel_size=(3, 3), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(42, 84, kernel_size=(3, 3), stride=(1, 1), padding=same)
        )
      )
      (15): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(42, 42, kernel_size=(1, 1), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(42, 84, kernel_size=(1, 1), stride=(1, 1), padding=same)
        )
      )
      (16): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(42, 42, kernel_size=(3, 3), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(42, 84, kernel_size=(3, 3), stride=(1, 1), padding=same)
        )
      )
      (17): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(42, 42, kernel_size=(1, 1), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(42, 84, kernel_size=(1, 1), stride=(1, 1), padding=same)
        )
      )
    )
  )
  (anomaly_map_generator): AnomalyMapGenerator()
), 'loss': UFlowLoss()}
prepare_data_per_node: True
allow_zero_length_dataloader_with_multiple_devices: False
_log_hyperparams: True
_dtype: torch.float32
_device: cpu
_trainer: <lightning.pytorch.trainer.trainer.Trainer object at 0x000002132C56A710>
_example_input_array: None
_automatic_optimization: True
_strict_loading: None
_current_fx_name: None
_param_requires_grad_state: {}
_metric_attributes: None
_compiler_ctx: None
_fabric: None
_fabric_optimizers: []
_device_mesh: None
_hparams_name: kwargs
_hparams: "affine_clamp":                 2.0
"affine_subnet_channels_ratio": 1.0
"backbone":                     resnet18
"evaluator":                    True
"flow_steps":                   4
"permute_soft":                 False
"post_processor":               True
"pre_processor":                True
"visualizer":                   True
_hparams_initial: "affine_clamp":                 2.0
"affine_subnet_channels_ratio": 1.0
"backbone":                     resnet18
"evaluator":                    True
"flow_steps":                   4
"permute_soft":                 False
"post_processor":               True
"pre_processor":                True
"visualizer":                   True
visualizer: <anomalib.visualization.image.visualizer.ImageVisualizer object at 0x000002132C4A90F0>
_input_size: None
backbone: resnet18
flow_steps: 4
affine_clamp: 2.0
affine_subnet_channels_ratio: 1.0
permute_soft: False

--------------------------------------------------------
** Model Structure (named_children) **
--------------------------------------------------------
pre_processor:
PreProcessor(
  (transform): Compose(
        Resize(size=[448, 448], interpolation=InterpolationMode.BILINEAR, antialias=True)
        Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], inplace=False)
  )
  (export_transform): Compose(
        Resize(size=[448, 448], interpolation=InterpolationMode.BILINEAR, antialias=False)
        Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], inplace=False)
  )
)
post_processor:
PostProcessor(
  (_image_threshold_metric): F1AdaptiveThreshold()
  (_pixel_threshold_metric): F1AdaptiveThreshold()
  (_image_min_max_metric): MinMax()
  (_pixel_min_max_metric): MinMax()
)
evaluator:
Evaluator(
  (val_metrics): ModuleList()
  (test_metrics): ModuleList(
    (0): AUROC()
    (1): F1Score()
    (2): AUROC()
    (3): F1Score()
  )
)
model:
UflowModel(
  (feature_extractor): LayerNormFeatureExtractor(
    (feature_extractor): FeatureListNet(
      (conv1): Conv2d(3, 64, kernel_size=(7, 7), stride=(2, 2), padding=(3, 3), bias=False)
      (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (act1): ReLU(inplace=True)
      (maxpool): MaxPool2d(kernel_size=3, stride=2, padding=1, dilation=1, ceil_mode=False)
      (layer1): Sequential(
        (0): BasicBlock(
          (conv1): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (drop_block): Identity()
          (act1): ReLU(inplace=True)
          (aa): Identity()
          (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (act2): ReLU(inplace=True)
        )
        (1): BasicBlock(
          (conv1): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (drop_block): Identity()
          (act1): ReLU(inplace=True)
          (aa): Identity()
          (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (act2): ReLU(inplace=True)
        )
      )
      (layer2): Sequential(
        (0): BasicBlock(
          (conv1): Conv2d(64, 128, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
          (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (drop_block): Identity()
          (act1): ReLU(inplace=True)
          (aa): Identity()
          (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (act2): ReLU(inplace=True)
          (downsample): Sequential(
            (0): Conv2d(64, 128, kernel_size=(1, 1), stride=(2, 2), bias=False)
            (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          )
        )
        (1): BasicBlock(
          (conv1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (drop_block): Identity()
          (act1): ReLU(inplace=True)
          (aa): Identity()
          (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (act2): ReLU(inplace=True)
        )
      )
      (layer3): Sequential(
        (0): BasicBlock(
          (conv1): Conv2d(128, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
          (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (drop_block): Identity()
          (act1): ReLU(inplace=True)
          (aa): Identity()
          (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (act2): ReLU(inplace=True)
          (downsample): Sequential(
            (0): Conv2d(128, 256, kernel_size=(1, 1), stride=(2, 2), bias=False)
            (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          )
        )
        (1): BasicBlock(
          (conv1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (drop_block): Identity()
          (act1): ReLU(inplace=True)
          (aa): Identity()
          (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
          (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (act2): ReLU(inplace=True)
        )
      )
    )
    (feature_normalizations): ModuleList(
      (0): LayerNorm((64, 112, 112), eps=1e-05, elementwise_affine=True)
      (1): LayerNorm((128, 56, 56), eps=1e-05, elementwise_affine=True)
      (2): LayerNorm((256, 28, 28), eps=1e-05, elementwise_affine=True)
    )
  )
  (flow): GraphINN(
    (module_list): ModuleList(
      (0): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(128, 256, kernel_size=(3, 3), stride=(1, 1), padding=same)
        )
      )
      (1): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(128, 128, kernel_size=(1, 1), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1), padding=same)
        )
      )
      (2): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(128, 256, kernel_size=(3, 3), stride=(1, 1), padding=same)
        )
      )
      (3): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(128, 128, kernel_size=(1, 1), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1), padding=same)
        )
      )
      (4): Split()
      (5): IRevNetUpsampling()
      (6): Concat()
      (7): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(80, 80, kernel_size=(3, 3), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(80, 160, kernel_size=(3, 3), stride=(1, 1), padding=same)
        )
      )
      (8): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(80, 80, kernel_size=(1, 1), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(80, 160, kernel_size=(1, 1), stride=(1, 1), padding=same)
        )
      )
      (9): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(80, 80, kernel_size=(3, 3), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(80, 160, kernel_size=(3, 3), stride=(1, 1), padding=same)
        )
      )
      (10): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(80, 80, kernel_size=(1, 1), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(80, 160, kernel_size=(1, 1), stride=(1, 1), padding=same)
        )
      )
      (11): Split()
      (12): IRevNetUpsampling()
      (13): Concat()
      (14): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(42, 42, kernel_size=(3, 3), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(42, 84, kernel_size=(3, 3), stride=(1, 1), padding=same)
        )
      )
      (15): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(42, 42, kernel_size=(1, 1), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(42, 84, kernel_size=(1, 1), stride=(1, 1), padding=same)
        )
      )
      (16): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(42, 42, kernel_size=(3, 3), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(42, 84, kernel_size=(3, 3), stride=(1, 1), padding=same)
        )
      )
      (17): AllInOneBlock(
        (subnet): Sequential(
          (0): Conv2d(42, 42, kernel_size=(1, 1), stride=(1, 1), padding=same)
          (1): ReLU()
          (2): Conv2d(42, 84, kernel_size=(1, 1), stride=(1, 1), padding=same)
        )
      )
    )
  )
  (anomaly_map_generator): AnomalyMapGenerator()
)
loss:
UFlowLoss()

--------------------------------------------------------
** Model Parameters (named_parameters) **
--------------------------------------------------------
model.feature_extractor.feature_extractor.conv1.weight - shape: (64, 3, 7, 7)
model.feature_extractor.feature_extractor.bn1.weight - shape: (64,)
model.feature_extractor.feature_extractor.bn1.bias - shape: (64,)
model.feature_extractor.feature_extractor.layer1.0.conv1.weight - shape: (64, 64, 3, 3)
model.feature_extractor.feature_extractor.layer1.0.bn1.weight - shape: (64,)
model.feature_extractor.feature_extractor.layer1.0.bn1.bias - shape: (64,)
model.feature_extractor.feature_extractor.layer1.0.conv2.weight - shape: (64, 64, 3, 3)
model.feature_extractor.feature_extractor.layer1.0.bn2.weight - shape: (64,)
model.feature_extractor.feature_extractor.layer1.0.bn2.bias - shape: (64,)
model.feature_extractor.feature_extractor.layer1.1.conv1.weight - shape: (64, 64, 3, 3)
model.feature_extractor.feature_extractor.layer1.1.bn1.weight - shape: (64,)
model.feature_extractor.feature_extractor.layer1.1.bn1.bias - shape: (64,)
model.feature_extractor.feature_extractor.layer1.1.conv2.weight - shape: (64, 64, 3, 3)
model.feature_extractor.feature_extractor.layer1.1.bn2.weight - shape: (64,)
model.feature_extractor.feature_extractor.layer1.1.bn2.bias - shape: (64,)
model.feature_extractor.feature_extractor.layer2.0.conv1.weight - shape: (128, 64, 3, 3)
model.feature_extractor.feature_extractor.layer2.0.bn1.weight - shape: (128,)
model.feature_extractor.feature_extractor.layer2.0.bn1.bias - shape: (128,)
model.feature_extractor.feature_extractor.layer2.0.conv2.weight - shape: (128, 128, 3, 3)
model.feature_extractor.feature_extractor.layer2.0.bn2.weight - shape: (128,)
model.feature_extractor.feature_extractor.layer2.0.bn2.bias - shape: (128,)
model.feature_extractor.feature_extractor.layer2.0.downsample.0.weight - shape: (128, 64, 1, 1)
model.feature_extractor.feature_extractor.layer2.0.downsample.1.weight - shape: (128,)
model.feature_extractor.feature_extractor.layer2.0.downsample.1.bias - shape: (128,)
model.feature_extractor.feature_extractor.layer2.1.conv1.weight - shape: (128, 128, 3, 3)
model.feature_extractor.feature_extractor.layer2.1.bn1.weight - shape: (128,)
model.feature_extractor.feature_extractor.layer2.1.bn1.bias - shape: (128,)
model.feature_extractor.feature_extractor.layer2.1.conv2.weight - shape: (128, 128, 3, 3)
model.feature_extractor.feature_extractor.layer2.1.bn2.weight - shape: (128,)
model.feature_extractor.feature_extractor.layer2.1.bn2.bias - shape: (128,)
model.feature_extractor.feature_extractor.layer3.0.conv1.weight - shape: (256, 128, 3, 3)
model.feature_extractor.feature_extractor.layer3.0.bn1.weight - shape: (256,)
model.feature_extractor.feature_extractor.layer3.0.bn1.bias - shape: (256,)
model.feature_extractor.feature_extractor.layer3.0.conv2.weight - shape: (256, 256, 3, 3)
model.feature_extractor.feature_extractor.layer3.0.bn2.weight - shape: (256,)
model.feature_extractor.feature_extractor.layer3.0.bn2.bias - shape: (256,)
model.feature_extractor.feature_extractor.layer3.0.downsample.0.weight - shape: (256, 128, 1, 1)
model.feature_extractor.feature_extractor.layer3.0.downsample.1.weight - shape: (256,)
model.feature_extractor.feature_extractor.layer3.0.downsample.1.bias - shape: (256,)
model.feature_extractor.feature_extractor.layer3.1.conv1.weight - shape: (256, 256, 3, 3)
model.feature_extractor.feature_extractor.layer3.1.bn1.weight - shape: (256,)
model.feature_extractor.feature_extractor.layer3.1.bn1.bias - shape: (256,)
model.feature_extractor.feature_extractor.layer3.1.conv2.weight - shape: (256, 256, 3, 3)
model.feature_extractor.feature_extractor.layer3.1.bn2.weight - shape: (256,)
model.feature_extractor.feature_extractor.layer3.1.bn2.bias - shape: (256,)
model.feature_extractor.feature_normalizations.0.weight - shape: (64, 112, 112)
model.feature_extractor.feature_normalizations.0.bias - shape: (64, 112, 112)
model.feature_extractor.feature_normalizations.1.weight - shape: (128, 56, 56)
model.feature_extractor.feature_normalizations.1.bias - shape: (128, 56, 56)
model.feature_extractor.feature_normalizations.2.weight - shape: (256, 28, 28)
model.feature_extractor.feature_normalizations.2.bias - shape: (256, 28, 28)
model.flow.module_list.0.global_scale - shape: (1, 256, 1, 1)
model.flow.module_list.0.global_offset - shape: (1, 256, 1, 1)
model.flow.module_list.0.w_perm - shape: (256, 256, 1, 1)
model.flow.module_list.0.w_perm_inv - shape: (256, 256, 1, 1)
model.flow.module_list.0.subnet.0.weight - shape: (128, 128, 3, 3)
model.flow.module_list.0.subnet.0.bias - shape: (128,)
model.flow.module_list.0.subnet.2.weight - shape: (256, 128, 3, 3)
model.flow.module_list.0.subnet.2.bias - shape: (256,)
model.flow.module_list.1.global_scale - shape: (1, 256, 1, 1)
model.flow.module_list.1.global_offset - shape: (1, 256, 1, 1)
model.flow.module_list.1.w_perm - shape: (256, 256, 1, 1)
model.flow.module_list.1.w_perm_inv - shape: (256, 256, 1, 1)
model.flow.module_list.1.subnet.0.weight - shape: (128, 128, 1, 1)
model.flow.module_list.1.subnet.0.bias - shape: (128,)
model.flow.module_list.1.subnet.2.weight - shape: (256, 128, 1, 1)
model.flow.module_list.1.subnet.2.bias - shape: (256,)
model.flow.module_list.2.global_scale - shape: (1, 256, 1, 1)
model.flow.module_list.2.global_offset - shape: (1, 256, 1, 1)
model.flow.module_list.2.w_perm - shape: (256, 256, 1, 1)
model.flow.module_list.2.w_perm_inv - shape: (256, 256, 1, 1)
model.flow.module_list.2.subnet.0.weight - shape: (128, 128, 3, 3)
model.flow.module_list.2.subnet.0.bias - shape: (128,)
model.flow.module_list.2.subnet.2.weight - shape: (256, 128, 3, 3)
model.flow.module_list.2.subnet.2.bias - shape: (256,)
model.flow.module_list.3.global_scale - shape: (1, 256, 1, 1)
model.flow.module_list.3.global_offset - shape: (1, 256, 1, 1)
model.flow.module_list.3.w_perm - shape: (256, 256, 1, 1)
model.flow.module_list.3.w_perm_inv - shape: (256, 256, 1, 1)
model.flow.module_list.3.subnet.0.weight - shape: (128, 128, 1, 1)
model.flow.module_list.3.subnet.0.bias - shape: (128,)
model.flow.module_list.3.subnet.2.weight - shape: (256, 128, 1, 1)
model.flow.module_list.3.subnet.2.bias - shape: (256,)
model.flow.module_list.5.downsample_kernel - shape: (128, 1, 2, 2)
model.flow.module_list.7.global_scale - shape: (1, 160, 1, 1)
model.flow.module_list.7.global_offset - shape: (1, 160, 1, 1)
model.flow.module_list.7.w_perm - shape: (160, 160, 1, 1)
model.flow.module_list.7.w_perm_inv - shape: (160, 160, 1, 1)
model.flow.module_list.7.subnet.0.weight - shape: (80, 80, 3, 3)
model.flow.module_list.7.subnet.0.bias - shape: (80,)
model.flow.module_list.7.subnet.2.weight - shape: (160, 80, 3, 3)
model.flow.module_list.7.subnet.2.bias - shape: (160,)
model.flow.module_list.8.global_scale - shape: (1, 160, 1, 1)
model.flow.module_list.8.global_offset - shape: (1, 160, 1, 1)
model.flow.module_list.8.w_perm - shape: (160, 160, 1, 1)
model.flow.module_list.8.w_perm_inv - shape: (160, 160, 1, 1)
model.flow.module_list.8.subnet.0.weight - shape: (80, 80, 1, 1)
model.flow.module_list.8.subnet.0.bias - shape: (80,)
model.flow.module_list.8.subnet.2.weight - shape: (160, 80, 1, 1)
model.flow.module_list.8.subnet.2.bias - shape: (160,)
model.flow.module_list.9.global_scale - shape: (1, 160, 1, 1)
model.flow.module_list.9.global_offset - shape: (1, 160, 1, 1)
model.flow.module_list.9.w_perm - shape: (160, 160, 1, 1)
model.flow.module_list.9.w_perm_inv - shape: (160, 160, 1, 1)
model.flow.module_list.9.subnet.0.weight - shape: (80, 80, 3, 3)
model.flow.module_list.9.subnet.0.bias - shape: (80,)
model.flow.module_list.9.subnet.2.weight - shape: (160, 80, 3, 3)
model.flow.module_list.9.subnet.2.bias - shape: (160,)
model.flow.module_list.10.global_scale - shape: (1, 160, 1, 1)
model.flow.module_list.10.global_offset - shape: (1, 160, 1, 1)
model.flow.module_list.10.w_perm - shape: (160, 160, 1, 1)
model.flow.module_list.10.w_perm_inv - shape: (160, 160, 1, 1)
model.flow.module_list.10.subnet.0.weight - shape: (80, 80, 1, 1)
model.flow.module_list.10.subnet.0.bias - shape: (80,)
model.flow.module_list.10.subnet.2.weight - shape: (160, 80, 1, 1)
model.flow.module_list.10.subnet.2.bias - shape: (160,)
model.flow.module_list.12.downsample_kernel - shape: (80, 1, 2, 2)
model.flow.module_list.14.global_scale - shape: (1, 84, 1, 1)
model.flow.module_list.14.global_offset - shape: (1, 84, 1, 1)
model.flow.module_list.14.w_perm - shape: (84, 84, 1, 1)
model.flow.module_list.14.w_perm_inv - shape: (84, 84, 1, 1)
model.flow.module_list.14.subnet.0.weight - shape: (42, 42, 3, 3)
model.flow.module_list.14.subnet.0.bias - shape: (42,)
model.flow.module_list.14.subnet.2.weight - shape: (84, 42, 3, 3)
model.flow.module_list.14.subnet.2.bias - shape: (84,)
model.flow.module_list.15.global_scale - shape: (1, 84, 1, 1)
model.flow.module_list.15.global_offset - shape: (1, 84, 1, 1)
model.flow.module_list.15.w_perm - shape: (84, 84, 1, 1)
model.flow.module_list.15.w_perm_inv - shape: (84, 84, 1, 1)
model.flow.module_list.15.subnet.0.weight - shape: (42, 42, 1, 1)
model.flow.module_list.15.subnet.0.bias - shape: (42,)
model.flow.module_list.15.subnet.2.weight - shape: (84, 42, 1, 1)
model.flow.module_list.15.subnet.2.bias - shape: (84,)
model.flow.module_list.16.global_scale - shape: (1, 84, 1, 1)
model.flow.module_list.16.global_offset - shape: (1, 84, 1, 1)
model.flow.module_list.16.w_perm - shape: (84, 84, 1, 1)
model.flow.module_list.16.w_perm_inv - shape: (84, 84, 1, 1)
model.flow.module_list.16.subnet.0.weight - shape: (42, 42, 3, 3)
model.flow.module_list.16.subnet.0.bias - shape: (42,)
model.flow.module_list.16.subnet.2.weight - shape: (84, 42, 3, 3)
model.flow.module_list.16.subnet.2.bias - shape: (84,)
model.flow.module_list.17.global_scale - shape: (1, 84, 1, 1)
model.flow.module_list.17.global_offset - shape: (1, 84, 1, 1)
model.flow.module_list.17.w_perm - shape: (84, 84, 1, 1)
model.flow.module_list.17.w_perm_inv - shape: (84, 84, 1, 1)
model.flow.module_list.17.subnet.0.weight - shape: (42, 42, 1, 1)
model.flow.module_list.17.subnet.0.bias - shape: (42,)
model.flow.module_list.17.subnet.2.weight - shape: (84, 42, 1, 1)
model.flow.module_list.17.subnet.2.bias - shape: (84,)

--------------------------------------------------------
** Engine Parameters **
--------------------------------------------------------
_cache: <anomalib.engine.engine._TrainerArgumentsCache object at 0x000002132C56A5C0>
_trainer: <lightning.pytorch.trainer.trainer.Trainer object at 0x000002132C56A710>

--------------------------------------------------------
** Trainer Parameters (réel, après .fit) **
--------------------------------------------------------
_model_registry: None
barebones: False
_data_connector: <lightning.pytorch.trainer.connectors.data_connector._DataConnector object at 0x000002132C56A860>
_accelerator_connector: <lightning.pytorch.trainer.connectors.accelerator_connector._AcceleratorConnector object at 0x000002132C56A6B0>
_logger_connector: <lightning.pytorch.trainer.connectors.logger_connector.logger_connector._LoggerConnector object at 0x000002132C56AB90>
_callback_connector: <lightning.pytorch.trainer.connectors.callback_connector._CallbackConnector object at 0x000002132C56AC80>
_checkpoint_connector: <lightning.pytorch.trainer.connectors.checkpoint_connector._CheckpointConnector object at 0x000002132C56A7D0>
_signal_connector: <lightning.pytorch.trainer.connectors.signal_connector._SignalConnector object at 0x000002132C56AB00>
fit_loop: <lightning.pytorch.loops.fit_loop._FitLoop object at 0x000002132C56A8C0>
validate_loop: <lightning.pytorch.loops.evaluation_loop._EvaluationLoop object at 0x000002132C56AA10>
test_loop: <lightning.pytorch.loops.evaluation_loop._EvaluationLoop object at 0x000002132C56B6A0>
predict_loop: <lightning.pytorch.loops.prediction_loop._PredictionLoop object at 0x000002132C56B520>
accumulate_grad_batches: 1
_default_root_dir: C:\Users\<USER>\Documents\uflow\results_uflow\training\Uflow\test_training\latest
callbacks: [<anomalib.callbacks.timer.TimerCallback object at 0x000002132C56A770>, <utils.save_outputs.SaveOutputsCallback object at 0x000002132C56A530>, <lightning.pytorch.callbacks.progress.tqdm_progress.TQDMProgressBar object at 0x000002132C56B130>, <lightning.pytorch.callbacks.model_summary.ModelSummary object at 0x000002132C56B0A0>, PreProcessor(
  (transform): Compose(
        Resize(size=[448, 448], interpolation=InterpolationMode.BILINEAR, antialias=True)
        Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], inplace=False)
  )
  (export_transform): Compose(
        Resize(size=[448, 448], interpolation=InterpolationMode.BILINEAR, antialias=False)
        Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], inplace=False)
  )
), PostProcessor(
  (_image_threshold_metric): F1AdaptiveThreshold()
  (_pixel_threshold_metric): F1AdaptiveThreshold()
  (_image_min_max_metric): MinMax()
  (_pixel_min_max_metric): MinMax()
), Evaluator(
  (val_metrics): ModuleList()
  (test_metrics): ModuleList(
    (0): AUROC()
    (1): F1Score()
    (2): AUROC()
    (3): F1Score()
  )
), <anomalib.visualization.image.visualizer.ImageVisualizer object at 0x000002132C4A90F0>, <anomalib.callbacks.checkpoint.ModelCheckpoint object at 0x000002132C56A500>]
datamodule: {Train dataloader: size=10}

{Validation dataloader: size=20}

{Test dataloader: size=20}

{Predict dataloader: size=20}
check_val_every_n_epoch: 1
reload_dataloaders_every_n_epochs: 0
gradient_clip_val: None
gradient_clip_algorithm: None
_detect_anomaly: False
should_stop: False
state: TrainerState(status=<TrainerStatus.FINISHED: 'finished'>, fn=<TrainerFn.TESTING: 'test'>, stage=None)
profiler: <lightning.pytorch.profilers.base.PassThroughProfiler object at 0x000002132C56AD70>
_loggers: [<lightning.pytorch.loggers.tensorboard.TensorBoardLogger object at 0x0000021338544B20>]
log_every_n_steps: 50
fast_dev_run: False
overfit_batches: 0.0
limit_train_batches: 1.0
limit_val_batches: 1.0
limit_test_batches: 1.0
limit_predict_batches: 1.0
num_sanity_val_steps: 0
val_check_interval: 1.0
val_check_batch: 5

-------------------- Trainer Attributes - ALL --------------------
accelerator: <lightning.pytorch.accelerators.cuda.CUDAAccelerator object at 0x000002132C56A830>
accumulate_grad_batches: 1
barebones: False
callback_metrics: {'image_AUROC': tensor(0.1500), 'image_F1Score': tensor(0.6207), 'pixel_AUROC': tensor(0.5650), 'pixel_F1Score': tensor(0.0021)}
callbacks: [<anomalib.callbacks.timer.TimerCallback object at 0x000002132C56A770>, <utils.save_outputs.SaveOutputsCallback object at 0x000002132C56A530>, <lightning.pytorch.callbacks.progress.tqdm_progress.TQDMProgressBar object at 0x000002132C56B130>, <lightning.pytorch.callbacks.model_summary.ModelSummary object at 0x000002132C56B0A0>, PreProcessor(
  (transform): Compose(
        Resize(size=[448, 448], interpolation=InterpolationMode.BILINEAR, antialias=True)
        Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], inplace=False)
  )
  (export_transform): Compose(
        Resize(size=[448, 448], interpolation=InterpolationMode.BILINEAR, antialias=False)
        Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], inplace=False)
  )
), PostProcessor(
  (_image_threshold_metric): F1AdaptiveThreshold()
  (_pixel_threshold_metric): F1AdaptiveThreshold()
  (_image_min_max_metric): MinMax()
  (_pixel_min_max_metric): MinMax()
), Evaluator(
  (val_metrics): ModuleList()
  (test_metrics): ModuleList(
    (0): AUROC()
    (1): F1Score()
    (2): AUROC()
    (3): F1Score()
  )
), <anomalib.visualization.image.visualizer.ImageVisualizer object at 0x000002132C4A90F0>, <anomalib.callbacks.checkpoint.ModelCheckpoint object at 0x000002132C56A500>]
check_val_every_n_epoch: 1
checkpoint_callback: <anomalib.callbacks.checkpoint.ModelCheckpoint object at 0x000002132C56A500>
checkpoint_callbacks: [<anomalib.callbacks.checkpoint.ModelCheckpoint object at 0x000002132C56A500>]
ckpt_path: C:\Users\<USER>\Documents\uflow\results_uflow\training\Uflow\test_training\v1\version_0\checkpoints\epoch_epoch=000-loss=loss=1004642.6875.ckpt
current_epoch: 1
datamodule: {Train dataloader: size=10}

{Validation dataloader: size=20}

{Test dataloader: size=20}

{Predict dataloader: size=20}
default_root_dir: C:\Users\<USER>\Documents\uflow\results_uflow\training\Uflow\test_training\latest
device_ids: [0]
distributed_sampler_kwargs: None
early_stopping_callback: None
early_stopping_callbacks: []
enable_validation: True
estimated_stepping_batches: 5
evaluating: False
fast_dev_run: False
fit: <bound method Trainer.fit of <lightning.pytorch.trainer.trainer.Trainer object at 0x000002132C56A710>>
fit_loop: <lightning.pytorch.loops.fit_loop._FitLoop object at 0x000002132C56A8C0>
global_rank: 0
global_step: 5
gradient_clip_algorithm: None
gradient_clip_val: None
init_module: <bound method Trainer.init_module of <lightning.pytorch.trainer.trainer.Trainer object at 0x000002132C56A710>>
interrupted: False
is_global_zero: True
is_last_batch: True
lightning_module: Uflow(
  (pre_processor): PreProcessor(
    (transform): Compose(
          Resize(size=[448, 448], interpolation=InterpolationMode.BILINEAR, antialias=True)
          Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], inplace=False)
    )
    (export_transform): Compose(
          Resize(size=[448, 448], interpolation=InterpolationMode.BILINEAR, antialias=False)
          Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], inplace=False)
    )
  )
  (post_processor): PostProcessor(
    (_image_threshold_metric): F1AdaptiveThreshold()
    (_pixel_threshold_metric): F1AdaptiveThreshold()
    (_image_min_max_metric): MinMax()
    (_pixel_min_max_metric): MinMax()
  )
  (evaluator): Evaluator(
    (val_metrics): ModuleList()
    (test_metrics): ModuleList(
      (0): AUROC()
      (1): F1Score()
      (2): AUROC()
      (3): F1Score()
    )
  )
  (model): UflowModel(
    (feature_extractor): LayerNormFeatureExtractor(
      (feature_extractor): FeatureListNet(
        (conv1): Conv2d(3, 64, kernel_size=(7, 7), stride=(2, 2), padding=(3, 3), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (act1): ReLU(inplace=True)
        (maxpool): MaxPool2d(kernel_size=3, stride=2, padding=1, dilation=1, ceil_mode=False)
        (layer1): Sequential(
          (0): BasicBlock(
            (conv1): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (drop_block): Identity()
            (act1): ReLU(inplace=True)
            (aa): Identity()
            (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (act2): ReLU(inplace=True)
          )
          (1): BasicBlock(
            (conv1): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (drop_block): Identity()
            (act1): ReLU(inplace=True)
            (aa): Identity()
            (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (act2): ReLU(inplace=True)
          )
        )
        (layer2): Sequential(
          (0): BasicBlock(
            (conv1): Conv2d(64, 128, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
            (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (drop_block): Identity()
            (act1): ReLU(inplace=True)
            (aa): Identity()
            (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (act2): ReLU(inplace=True)
            (downsample): Sequential(
              (0): Conv2d(64, 128, kernel_size=(1, 1), stride=(2, 2), bias=False)
              (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            )
          )
          (1): BasicBlock(
            (conv1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (drop_block): Identity()
            (act1): ReLU(inplace=True)
            (aa): Identity()
            (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (act2): ReLU(inplace=True)
          )
        )
        (layer3): Sequential(
          (0): BasicBlock(
            (conv1): Conv2d(128, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
            (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (drop_block): Identity()
            (act1): ReLU(inplace=True)
            (aa): Identity()
            (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (act2): ReLU(inplace=True)
            (downsample): Sequential(
              (0): Conv2d(128, 256, kernel_size=(1, 1), stride=(2, 2), bias=False)
              (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            )
          )
          (1): BasicBlock(
            (conv1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (drop_block): Identity()
            (act1): ReLU(inplace=True)
            (aa): Identity()
            (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (act2): ReLU(inplace=True)
          )
        )
      )
      (feature_normalizations): ModuleList(
        (0): LayerNorm((64, 112, 112), eps=1e-05, elementwise_affine=True)
        (1): LayerNorm((128, 56, 56), eps=1e-05, elementwise_affine=True)
        (2): LayerNorm((256, 28, 28), eps=1e-05, elementwise_affine=True)
      )
    )
    (flow): GraphINN(
      (module_list): ModuleList(
        (0): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(128, 256, kernel_size=(3, 3), stride=(1, 1), padding=same)
          )
        )
        (1): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(128, 128, kernel_size=(1, 1), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1), padding=same)
          )
        )
        (2): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(128, 256, kernel_size=(3, 3), stride=(1, 1), padding=same)
          )
        )
        (3): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(128, 128, kernel_size=(1, 1), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1), padding=same)
          )
        )
        (4): Split()
        (5): IRevNetUpsampling()
        (6): Concat()
        (7): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(80, 80, kernel_size=(3, 3), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(80, 160, kernel_size=(3, 3), stride=(1, 1), padding=same)
          )
        )
        (8): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(80, 80, kernel_size=(1, 1), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(80, 160, kernel_size=(1, 1), stride=(1, 1), padding=same)
          )
        )
        (9): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(80, 80, kernel_size=(3, 3), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(80, 160, kernel_size=(3, 3), stride=(1, 1), padding=same)
          )
        )
        (10): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(80, 80, kernel_size=(1, 1), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(80, 160, kernel_size=(1, 1), stride=(1, 1), padding=same)
          )
        )
        (11): Split()
        (12): IRevNetUpsampling()
        (13): Concat()
        (14): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(42, 42, kernel_size=(3, 3), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(42, 84, kernel_size=(3, 3), stride=(1, 1), padding=same)
          )
        )
        (15): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(42, 42, kernel_size=(1, 1), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(42, 84, kernel_size=(1, 1), stride=(1, 1), padding=same)
          )
        )
        (16): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(42, 42, kernel_size=(3, 3), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(42, 84, kernel_size=(3, 3), stride=(1, 1), padding=same)
          )
        )
        (17): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(42, 42, kernel_size=(1, 1), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(42, 84, kernel_size=(1, 1), stride=(1, 1), padding=same)
          )
        )
      )
    )
    (anomaly_map_generator): AnomalyMapGenerator()
  )
  (loss): UFlowLoss()
)
limit_predict_batches: 1.0
limit_test_batches: 1.0
limit_train_batches: 1.0
limit_val_batches: 1.0
local_rank: 0
log_dir: C:\Users\<USER>\Documents\uflow\results_uflow\training\Uflow/test_training/latest\version_0
log_every_n_steps: 50
logged_metrics: {'image_AUROC': tensor(0.1500), 'image_F1Score': tensor(0.6207), 'pixel_AUROC': tensor(0.5650), 'pixel_F1Score': tensor(0.0021)}
logger: <lightning.pytorch.loggers.tensorboard.TensorBoardLogger object at 0x0000021338544B20>
loggers: [<lightning.pytorch.loggers.tensorboard.TensorBoardLogger object at 0x0000021338544B20>]
lr_scheduler_configs: [LRSchedulerConfig(scheduler=<torch.optim.lr_scheduler.LinearLR object at 0x000002132C4A9960>, name=None, interval='epoch', frequency=1, reduce_on_plateau=False, monitor=None, strict=True)]
max_epochs: 1
max_steps: -1
min_epochs: 0
min_steps: None
model: Uflow(
  (pre_processor): PreProcessor(
    (transform): Compose(
          Resize(size=[448, 448], interpolation=InterpolationMode.BILINEAR, antialias=True)
          Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], inplace=False)
    )
    (export_transform): Compose(
          Resize(size=[448, 448], interpolation=InterpolationMode.BILINEAR, antialias=False)
          Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], inplace=False)
    )
  )
  (post_processor): PostProcessor(
    (_image_threshold_metric): F1AdaptiveThreshold()
    (_pixel_threshold_metric): F1AdaptiveThreshold()
    (_image_min_max_metric): MinMax()
    (_pixel_min_max_metric): MinMax()
  )
  (evaluator): Evaluator(
    (val_metrics): ModuleList()
    (test_metrics): ModuleList(
      (0): AUROC()
      (1): F1Score()
      (2): AUROC()
      (3): F1Score()
    )
  )
  (model): UflowModel(
    (feature_extractor): LayerNormFeatureExtractor(
      (feature_extractor): FeatureListNet(
        (conv1): Conv2d(3, 64, kernel_size=(7, 7), stride=(2, 2), padding=(3, 3), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (act1): ReLU(inplace=True)
        (maxpool): MaxPool2d(kernel_size=3, stride=2, padding=1, dilation=1, ceil_mode=False)
        (layer1): Sequential(
          (0): BasicBlock(
            (conv1): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (drop_block): Identity()
            (act1): ReLU(inplace=True)
            (aa): Identity()
            (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (act2): ReLU(inplace=True)
          )
          (1): BasicBlock(
            (conv1): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (drop_block): Identity()
            (act1): ReLU(inplace=True)
            (aa): Identity()
            (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (act2): ReLU(inplace=True)
          )
        )
        (layer2): Sequential(
          (0): BasicBlock(
            (conv1): Conv2d(64, 128, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
            (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (drop_block): Identity()
            (act1): ReLU(inplace=True)
            (aa): Identity()
            (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (act2): ReLU(inplace=True)
            (downsample): Sequential(
              (0): Conv2d(64, 128, kernel_size=(1, 1), stride=(2, 2), bias=False)
              (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            )
          )
          (1): BasicBlock(
            (conv1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (drop_block): Identity()
            (act1): ReLU(inplace=True)
            (aa): Identity()
            (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (act2): ReLU(inplace=True)
          )
        )
        (layer3): Sequential(
          (0): BasicBlock(
            (conv1): Conv2d(128, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
            (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (drop_block): Identity()
            (act1): ReLU(inplace=True)
            (aa): Identity()
            (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (act2): ReLU(inplace=True)
            (downsample): Sequential(
              (0): Conv2d(128, 256, kernel_size=(1, 1), stride=(2, 2), bias=False)
              (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            )
          )
          (1): BasicBlock(
            (conv1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (drop_block): Identity()
            (act1): ReLU(inplace=True)
            (aa): Identity()
            (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
            (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
            (act2): ReLU(inplace=True)
          )
        )
      )
      (feature_normalizations): ModuleList(
        (0): LayerNorm((64, 112, 112), eps=1e-05, elementwise_affine=True)
        (1): LayerNorm((128, 56, 56), eps=1e-05, elementwise_affine=True)
        (2): LayerNorm((256, 28, 28), eps=1e-05, elementwise_affine=True)
      )
    )
    (flow): GraphINN(
      (module_list): ModuleList(
        (0): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(128, 256, kernel_size=(3, 3), stride=(1, 1), padding=same)
          )
        )
        (1): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(128, 128, kernel_size=(1, 1), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1), padding=same)
          )
        )
        (2): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(128, 256, kernel_size=(3, 3), stride=(1, 1), padding=same)
          )
        )
        (3): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(128, 128, kernel_size=(1, 1), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1), padding=same)
          )
        )
        (4): Split()
        (5): IRevNetUpsampling()
        (6): Concat()
        (7): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(80, 80, kernel_size=(3, 3), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(80, 160, kernel_size=(3, 3), stride=(1, 1), padding=same)
          )
        )
        (8): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(80, 80, kernel_size=(1, 1), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(80, 160, kernel_size=(1, 1), stride=(1, 1), padding=same)
          )
        )
        (9): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(80, 80, kernel_size=(3, 3), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(80, 160, kernel_size=(3, 3), stride=(1, 1), padding=same)
          )
        )
        (10): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(80, 80, kernel_size=(1, 1), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(80, 160, kernel_size=(1, 1), stride=(1, 1), padding=same)
          )
        )
        (11): Split()
        (12): IRevNetUpsampling()
        (13): Concat()
        (14): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(42, 42, kernel_size=(3, 3), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(42, 84, kernel_size=(3, 3), stride=(1, 1), padding=same)
          )
        )
        (15): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(42, 42, kernel_size=(1, 1), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(42, 84, kernel_size=(1, 1), stride=(1, 1), padding=same)
          )
        )
        (16): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(42, 42, kernel_size=(3, 3), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(42, 84, kernel_size=(3, 3), stride=(1, 1), padding=same)
          )
        )
        (17): AllInOneBlock(
          (subnet): Sequential(
            (0): Conv2d(42, 42, kernel_size=(1, 1), stride=(1, 1), padding=same)
            (1): ReLU()
            (2): Conv2d(42, 84, kernel_size=(1, 1), stride=(1, 1), padding=same)
          )
        )
      )
    )
    (anomaly_map_generator): AnomalyMapGenerator()
  )
  (loss): UFlowLoss()
)
node_rank: 0
num_devices: 1
num_nodes: 1
num_predict_batches: []
num_sanity_val_batches: [0]
num_sanity_val_steps: 0
num_test_batches: [20]
num_training_batches: 5
num_val_batches: [20]
optimizers: [Adam (
Parameter Group 0
    amsgrad: False
    betas: (0.9, 0.999)
    capturable: False
    differentiable: False
    eps: 1e-08
    foreach: None
    fused: None
    initial_lr: 0.001
    lr: 0.000999976
    maximize: False
    weight_decay: 1e-05
)]
overfit_batches: 0.0
precision: 32-true
precision_plugin: <lightning.pytorch.plugins.precision.precision.Precision object at 0x000002132C56A950>
predict: <bound method Trainer.predict of <lightning.pytorch.trainer.trainer.Trainer object at 0x000002132C56A710>>
predict_dataloaders: None
predict_loop: <lightning.pytorch.loops.prediction_loop._PredictionLoop object at 0x000002132C56B520>
predicting: False
print: <bound method Trainer.print of <lightning.pytorch.trainer.trainer.Trainer object at 0x000002132C56A710>>
profiler: <lightning.pytorch.profilers.base.PassThroughProfiler object at 0x000002132C56AD70>
progress_bar_callback: <lightning.pytorch.callbacks.progress.tqdm_progress.TQDMProgressBar object at 0x000002132C56B130>
progress_bar_metrics: {}
received_sigterm: False
reload_dataloaders_every_n_epochs: 0
sanity_checking: False
save_checkpoint: <bound method Trainer.save_checkpoint of <lightning.pytorch.trainer.trainer.Trainer object at 0x000002132C56A710>>
scaler: None
should_stop: False
state: TrainerState(status=<TrainerStatus.FINISHED: 'finished'>, fn=<TrainerFn.TESTING: 'test'>, stage=None)
strategy: <lightning.pytorch.strategies.single_device.SingleDeviceStrategy object at 0x000002132C56A680>
test: <bound method Trainer.test of <lightning.pytorch.trainer.trainer.Trainer object at 0x000002132C56A710>>
test_dataloaders: <torch.utils.data.dataloader.DataLoader object at 0x000002150C1EBA90>
test_loop: <lightning.pytorch.loops.evaluation_loop._EvaluationLoop object at 0x000002132C56B6A0>
testing: False
train_dataloader: <torch.utils.data.dataloader.DataLoader object at 0x000002150C1E9750>
training: False
val_check_batch: 5
val_check_interval: 1.0
val_dataloaders: None
validate: <bound method Trainer.validate of <lightning.pytorch.trainer.trainer.Trainer object at 0x000002132C56A710>>
validate_loop: <lightning.pytorch.loops.evaluation_loop._EvaluationLoop object at 0x000002132C56AA10>
validating: False
world_size: 1

--------------------------------------------------------
** Trainer Device & Strategy Info **
--------------------------------------------------------
device_ids: [0]
root_device: cuda:0
strategy: SingleDeviceStrategy
accelerator: CUDAAccelerator

--------------------------------------------------------
** Callbacks **
--------------------------------------------------------
TimerCallback: <anomalib.callbacks.timer.TimerCallback object at 0x000002132C56A770>
SaveOutputsCallback: <utils.save_outputs.SaveOutputsCallback object at 0x000002132C56A530>
TQDMProgressBar: <lightning.pytorch.callbacks.progress.tqdm_progress.TQDMProgressBar object at 0x000002132C56B130>
ModelSummary: <lightning.pytorch.callbacks.model_summary.ModelSummary object at 0x000002132C56B0A0>
PreProcessor: PreProcessor(
  (transform): Compose(
        Resize(size=[448, 448], interpolation=InterpolationMode.BILINEAR, antialias=True)
        Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], inplace=False)
  )
  (export_transform): Compose(
        Resize(size=[448, 448], interpolation=InterpolationMode.BILINEAR, antialias=False)
        Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225], inplace=False)
  )
)
PostProcessor: PostProcessor(
  (_image_threshold_metric): F1AdaptiveThreshold()
  (_pixel_threshold_metric): F1AdaptiveThreshold()
  (_image_min_max_metric): MinMax()
  (_pixel_min_max_metric): MinMax()
)
Evaluator: Evaluator(
  (val_metrics): ModuleList()
  (test_metrics): ModuleList(
    (0): AUROC()
    (1): F1Score()
    (2): AUROC()
    (3): F1Score()
  )
)
ImageVisualizer: <anomalib.visualization.image.visualizer.ImageVisualizer object at 0x000002132C4A90F0>
ModelCheckpoint: <anomalib.callbacks.checkpoint.ModelCheckpoint object at 0x000002132C56A500>

--------------------------------------------------------
** Best Checkpoint **
--------------------------------------------------------
Path: C:\Users\<USER>\Documents\uflow\results_uflow\training\Uflow/test_training/latest\version_0\checkpoints\epoch_epoch=000-loss=loss=1004642.6875.ckpt
Score: 1004642.6875
