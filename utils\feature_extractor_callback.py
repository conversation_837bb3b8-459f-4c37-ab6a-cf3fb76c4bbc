"""Callback pour extraire les sorties du backbone pendant la prédiction.

Ce module fournit un callback qui permet de capturer les sorties du backbone
pendant la phase de prédiction. Ces sorties peuvent être utilisées pour l'analyse ou le débogage.
"""

from typing import Any
from pathlib import Path
import os

import torch
import numpy as np
from lightning.pytorch import Callback, Trainer
from lightning.pytorch.core import LightningModule
from anomalib.data import ImageBatch

class FeatureExtractorCallback(Callback):
    """Callback pour extraire les sorties du backbone pendant la prédiction.
    
    Ce callback capture uniquement les sorties du backbone du modèle pendant
    la phase de prédiction et les sauvegarde dans un fichier.
    
    Attributes:
        features (list[torch.Tensor]): Liste des sorties du backbone pour chaque batch.
        save_dir (Path): Dossier où sauvegarder les features.
    """
    
    def __init__(self, save_dir: str | Path | None = None) -> None:
        """Initialise le callback.
        
        Args:
            save_dir (str | Path | None): Dossier où sauvegarder les features.
                Si None, utilise le dossier de sortie par défaut du trainer.
        """
        super().__init__()
        self.features: list[torch.Tensor] = []
        self.save_dir = Path(save_dir) if save_dir else None
        
    def on_predict_batch_start(
        self,
        trainer: Trainer,
        pl_module: LightningModule,
        batch: Any,
        batch_idx: int,
        dataloader_idx: int = 0,
    ) -> None:
        """Capture les sorties du backbone au début de chaque batch de prédiction.
        
        Args:
            trainer (Trainer): Instance du trainer PyTorch Lightning.
            pl_module (LightningModule): Module PyTorch Lightning.
            batch (Any): Batch de données en cours de traitement.
            batch_idx (int): Index du batch.
            dataloader_idx (int, optional): Index du dataloader. Par défaut 0.
        """
        # Extraire les images du batch
        if isinstance(batch, ImageBatch):
            images = batch.image
        else:
            raise TypeError(f"Type de batch non supporté: {type(batch)}")
            
        # Extraire les features du backbone directement sans normalisation
        with torch.no_grad():
            features = pl_module.model.feature_extractor.extract_features(images)
        self.features.append(features)
        
    def on_predict_end(self, trainer: Trainer, pl_module: LightningModule) -> None:
        """Sauvegarde les features et réinitialise la liste.
        
        Args:
            trainer (Trainer): Instance du trainer PyTorch Lightning.
            pl_module (LightningModule): Module PyTorch Lightning.
        """
        if not self.features:
            return
            
        # Déterminer le dossier de sauvegarde
        save_dir = self.save_dir or Path(trainer.default_root_dir)
        save_dir = save_dir / "features"
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # Sauvegarder les features
        for i, features in enumerate(self.features):
            # Convertir en liste si c'est un dictionnaire
            if isinstance(features, dict):
                features = list(features.values())
            
            # Sauvegarder chaque niveau de features
            for j, feature in enumerate(features):
                # Convertir en numpy et sauvegarder
                feature_np = feature.cpu().numpy()
                save_path = save_dir / f"batch_{i}_level_{j}.npy"
                np.save(save_path, feature_np)
                
        # Réinitialiser la liste
        self.features = [] 