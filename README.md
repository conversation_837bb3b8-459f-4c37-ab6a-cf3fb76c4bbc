# Installation et Utilisation d'Anomalib

Voici chaque étape pour installer et utiliser Anomalib sous Windows avec Conda.

--------------------------------------------------------------------------------------------

1️ Installer Conda

--------------------------------------------------------------------------------------------

2 <USER> <GROUP> à jour :

conda update conda -y

--------------------------------------------------------------------------------------------

3 C<PERSON><PERSON> et Activer un Environnement Virtuel

conda create --name anomalib_env python=3.10 -y

--------------------------------------------------------------------------------------------

4 <USER> <GROUP>'environnement:

conda activate anomalib_env

Tu devrais voir `(anomalib_env)` au début de la ligne de commande.

--------------------------------------------------------------------------------------------

5 <USER> <GROUP> et les dépendances de base

  5.1 Installer pytorch conda

    conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia -y

  5.2 Vérifie si le GPU est détecté :

    nvidia-smi

  5.3 Vérifie que PyTorch trouve bien le GPU :

    python -c "import torch; print(torch.cuda.is_available())"

    Si `True`, c'est bon ! Sinon, revérifie l'installation de CUDA et des drivers.

  5.4 Installer anomalib:

    Clone le dépôt Anomalib dans un autre emplacement à part et installe 
    git clone https://github.com/open-edge-platform/anomalib.git
    cd anomalib
    pip install -e .
    
  5.6 Vérifie que l'installation s'est bien faite :

    anomalib --help

  5.7 Installer les dépendances de base:

    Retourne dans le dépot https://gitlab.evidentndtsystems.com/ai/uflow.git
    pip install -r requirements.txt
    python.exe -m pip install -U pip wheel setuptools

--------------------------------------------------------------------------------------------

6 <USER> <GROUP> Scripts Python

Le projet fournit deux scripts principaux pour l'entraînement et la prédiction :

### Entraînement (training.py)
```python
python training.py
```
Ce script configure et lance l'entraînement d'un modèle Uflow. Il gère :
- La configuration du modèle et des données
- L'entraînement avec TensorBoard
- La sauvegarde des checkpoints
- La génération des visualisations

### Prédiction (prediction.py)
```python
python prediction.py
```
Ce script permet de faire des prédictions avec un modèle entraîné. Il gère :
- Le chargement d'un checkpoint
- L'inférence sur de nouvelles images
- La sauvegarde des résultats

Pour plus de détails sur la configuration, consultez les commentaires dans les scripts.

--------------------------------------------------------------------------------------------

7 <USER> <GROUP> CLI (Optionnel)

Pour une utilisation via la ligne de commande, référez-vous à la documentation officielle d'Anomalib :
https://github.com/open-edge-platform/anomalib

--------------------------------------------------------------------------------------------

8 <USER> <GROUP> Datasets

Les scripts attendent la structure suivante pour les datasets :

├── datasets/TPI/
│     ├── train/
│     │    └── good/           (images normales pour l'entraînement)
│     ├── test/
│     │    ├── good/           (images normales pour le test)
│     │    └── defective/      (images défectueuses pour le test)
│     └── masks/
│          └── defective/      (masques des images défectueuses)

--------------------------------------------------------------------------------------------

9 Gérer les Problèmes

Si PyTorch n'utilise pas CUDA, Vérifie :
python -c "import torch; print(torch.cuda.is_available())"

Si anomalib n'est pas reconnu, Vérifie que tu es bien dans l'environnement Conda :
conda activate anomalib_env

Si problème de permissions, Essaie d'exécuter CMD en tant qu'administrateur.

Si problème de chemins trop longs:

  Windows a une limitation de 260 caractères pour les chemins. Pour éviter les problèmes :

  - Active le support des chemins longs dans Windows :
    - Ouvrir regedit
    - Aller à `HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem`
    - Modifier `LongPathsEnabled` à 1
    - Redémarrer l'ordinateur

  - Ou utilise des chemins plus courts :
    - Place tes dossiers de travail plus près de la racine (ex: C:\work\)
    - Évite les noms de dossiers trop longs
    - Utilise des chemins relatifs dans le code

--------------------------------------------------------------------------------------------