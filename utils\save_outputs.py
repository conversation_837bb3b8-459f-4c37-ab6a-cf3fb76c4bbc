from pathlib import Path
from lightning.pytorch import Callback
import numpy as np
from PIL import Image
import torch

        
class DebugCheckpointMetricsCallback(Callback):
    def on_validation_end(self, trainer, pl_module):
        metrics = trainer.callback_metrics
        print("\n🧪 Métriques disponibles pour ModelCheckpoint:")
        for k, v in metrics.items():
            print(f" • {k}: {v}")


class SaveOutputsCallback(Callback):
    def __init__(self, max_images=5):
        super().__init__()
        self.max_images = max_images
        self.saved = 0
        self.current_epoch = 0
        self.base_output_dir = None

    def on_validation_epoch_start(self, trainer, pl_module):
        self.saved = 0
        self.current_epoch = trainer.current_epoch
        self.base_output_dir = Path(pl_module.logger.log_dir).parent / "sortie_brute"

    def on_validation_batch_end(self, trainer, pl_module, outputs, batch, batch_idx, dataloader_idx=0):
        if self.saved >= self.max_images:
            return

        images = batch.image
        anomaly_maps = outputs.anomaly_map
        pred_masks = getattr(outputs, "pred_mask", None)
        scores = outputs.pred_score
        batch_size = images.shape[0]

        for i in range(batch_size):
            if self.saved >= self.max_images:
                break

            base = self.base_output_dir / f"epoch_{self.current_epoch:03d}" / f"image_{self.saved:06d}"

            # 📁 Création des sous-dossiers
            preproc_dir = base / "pre_processor"
            postproc_dir = base / "post_processor"
            model_dir = base / "model_outputs"
            for d in [preproc_dir, postproc_dir, model_dir]:
                d.mkdir(parents=True, exist_ok=True)

            # === Pré-processing ===
            np.save(preproc_dir / "input_tensor.npy", images[i].detach().cpu().numpy())

            img = images[i].detach().cpu()
            img = (img - img.min()) / (img.max() - img.min() + 1e-8)
            img_np = (img * 255).byte().permute(1, 2, 0).numpy()
            Image.fromarray(img_np).save(preproc_dir / "input.png")

            # === Post-processing ===
            amap = anomaly_maps[i].detach().cpu().squeeze().numpy()
            Image.fromarray((amap * 255).clip(0, 255).astype(np.uint8)).save(postproc_dir / "anomaly_map.png")
            np.save(postproc_dir / "anomaly_map.npy", amap)

            if pred_masks is not None:
                mask = pred_masks[i].detach().cpu().squeeze().numpy()
                Image.fromarray((mask * 255).astype(np.uint8)).save(postproc_dir / "pred_mask.png")

            with open(postproc_dir / "score.txt", "w") as f:
                f.write(str(scores[i].item()))

            threshold = getattr(pl_module.post_processor, "pixel_threshold", None)
            if threshold is not None:
                with open(postproc_dir / "threshold.txt", "w") as f:
                    f.write(str(threshold))

            # === Modèle : z & ljd ===
            img_tensor = images[i].unsqueeze(0).to(pl_module.device)

            with torch.no_grad():
                was_training = pl_module.model.training
                pl_module.model.train()
                
                # Extraire les caractéristiques du backbone
                backbone_features = pl_module.model.feature_extractor(img_tensor)
                
                # Continuer avec le flux normal
                z, ljd = pl_module.model(img_tensor)
                pl_module.model.train(was_training)

            # Sauvegarder les caractéristiques du backbone
            if isinstance(backbone_features, torch.Tensor):
                np.save(model_dir / "backbone_features.npy", backbone_features.detach().cpu().numpy())
            elif isinstance(backbone_features, (list, tuple)):
                for idx, feat in enumerate(backbone_features):
                    if isinstance(feat, torch.Tensor):
                        np.save(model_dir / f"backbone_features_{idx}.npy", feat.detach().cpu().numpy())

            for idx, z_level in enumerate(z):
                z_np = z_level[0].detach().cpu().numpy()
                np.save(model_dir / f"z_level{idx}.npy", z_np)

            with open(model_dir / "ljd.txt", "w") as f:
                f.write(f"{ljd.item():.6f}")

            self.saved += 1

