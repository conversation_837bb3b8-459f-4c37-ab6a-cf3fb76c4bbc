from pathlib import Path
import logging
import torch
from anomalib.data import PredictDataset
from anomalib.engine import Engine
from anomalib.models import Uflow
from utils.sauvegarde import save_prediction_outputs, save_script_copy
from utils.feature_extractor_callback import FeatureExtractorCallback
from lightning.pytorch.callbacks import ModelCheckpoint

# ========== CONFIGURATION ==========

# Configuration des Tensor Cores
torch.set_float32_matmul_precision('medium')

# Chemin vers le checkpoint du modèle entraîné à utiliser pour la prédiction
CKPT_PATH = Path("results_uflow/training/Uflow/test_training/latest/version_0/checkpoints/epoch_epoch=023-loss=loss=-955577.4375.ckpt").resolve()
if not CKPT_PATH.exists():
    raise FileNotFoundError(f"Checkpoint introuvable : {CKPT_PATH}")

# Dossier racine où seront stockés les résultats de prédiction
ROOT_BASE = Path("results_uflow/prediction").resolve()

# Dossier contenant les images à prédire
DATASET_ROOT = Path("datasets/test_prediction").resolve()

# Nom du dataset, extrait du chemin (ex: "defective")
DATASET_NAME = DATASET_ROOT.name

# === Dossier versionné de sortie ===
PREDICT_ROOT = ROOT_BASE / "predictions" / DATASET_NAME
version_counter = 0
while (PREDICT_ROOT / f"v{version_counter}").exists():
    version_counter += 1

REAL_PREDICT_DIR = PREDICT_ROOT / f"v{version_counter}"
REAL_PREDICT_DIR.mkdir(parents=True, exist_ok=True)

# ========== INITIALISATION ==========
logging.getLogger("anomalib.visualization.image.item_visualizer").setLevel(logging.ERROR)

model = Uflow(
    backbone="resnet18", #'mcait', 'resnet18', 'wide_resnet50_2'.
    flow_steps=4,
    affine_clamp=2.0,
    affine_subnet_channels_ratio=1.0,
    permute_soft=False,
    pre_processor=True,
    post_processor=True,
    evaluator=True,
    visualizer=True
)

# Callback pour la sauvegarde des checkpoints
checkpoint_callback = ModelCheckpoint(
    monitor="loss",
    mode="min",
    every_n_epochs=1
)

engine = Engine(
    accelerator="auto",
    devices="auto",
    default_root_dir=REAL_PREDICT_DIR,
    callbacks=[checkpoint_callback, FeatureExtractorCallback()]
)

dataset = PredictDataset(
    path=DATASET_ROOT,
)

# ========== PRÉDICTION ==========
predictions = engine.predict(
    model=model,
    dataset=dataset,
    ckpt_path=str(CKPT_PATH),
    return_predictions=True
)

# ========== SAUVEGARDE ==========
save_prediction_outputs(
    predictions=predictions,
    dataset_root=DATASET_ROOT,
    predict_root=REAL_PREDICT_DIR
)

# === Sauvegarde du script ===
save_script_copy(REAL_PREDICT_DIR, source_script=Path(__file__))

# === FIN ===
print(f"\n✅ Prédiction terminée. Résultats dans :\n{REAL_PREDICT_DIR}")
